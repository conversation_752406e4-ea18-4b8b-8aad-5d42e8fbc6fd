import subprocess
import os
from config import REALESRGAN_PATH

def process_with_ffmpeg(input_path, output_path, filters):
    filter_string = ",".join(filters)
    command = [
        'ffmpeg',
        '-i', input_path,
        '-vf', filter_string,
        '-c:v', 'libx264',
        '-crf', '18',
        '-preset', 'slow',
        output_path
    ]
    subprocess.run(command, check=True)

def process_with_realesrgan(input_path, output_path, model='realesrgan-x4plus'):
    """
    Procesa video con Real-ESRGAN extrayendo frames, procesándolos y rearmando el video.
    Si Real-ESRGAN falla, usa FFmpeg como fallback.
    """
    import tempfile
    import shutil

    # Crear directorio temporal para frames
    temp_dir = tempfile.mkdtemp()
    frames_dir = os.path.join(temp_dir, "frames")
    upscaled_dir = os.path.join(temp_dir, "upscaled")
    os.makedirs(frames_dir, exist_ok=True)
    os.makedirs(upscaled_dir, exist_ok=True)

    try:
        print("Extrayendo frames del video...")
        # Extraer frames del video
        extract_command = [
            'ffmpeg', '-i', input_path,
            '-vf', 'fps=30',  # Limitar a 30 fps para reducir procesamiento
            os.path.join(frames_dir, 'frame_%06d.png')
        ]
        subprocess.run(extract_command, check=True)

        print(f"Procesando frames con Real-ESRGAN (modelo: {model})...")
        # Procesar frames con Real-ESRGAN
        realesrgan_command = [
            os.path.join(REALESRGAN_PATH, "realesrgan-ncnn-vulkan"),
            "-i", frames_dir,
            "-o", upscaled_dir,
            "-n", model,
            "-f", "png"
        ]
        subprocess.run(realesrgan_command, check=True)

        print("Rearmando video desde frames procesados...")
        # Rearmar video desde frames procesados
        reassemble_command = [
            'ffmpeg', '-framerate', '30',
            '-i', os.path.join(upscaled_dir, 'frame_%06d.png'),
            '-c:v', 'libx264', '-crf', '18', '-preset', 'slow',
            '-pix_fmt', 'yuv420p',
            output_path
        ]
        subprocess.run(reassemble_command, check=True)

    except subprocess.CalledProcessError as e:
        print(f"Error con Real-ESRGAN: {e}")
        print("Usando FFmpeg como fallback...")
        # Fallback a FFmpeg con upscaling de alta calidad
        fallback_command = [
            'ffmpeg', '-i', input_path,
            '-vf', 'scale=1920:1080:flags=lanczos',
            '-c:v', 'libx264', '-crf', '18', '-preset', 'slow',
            output_path
        ]
        subprocess.run(fallback_command, check=True)

    finally:
        # Limpiar directorio temporal
        shutil.rmtree(temp_dir, ignore_errors=True)
