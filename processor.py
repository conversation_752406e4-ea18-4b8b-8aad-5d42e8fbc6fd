import subprocess
import os
import re
import glob
from config import REALESRGAN_PATH

def get_video_duration(input_path):
    """Obtiene la duración del video en segundos usando ffprobe."""
    try:
        command = [
            'ffprobe', '-v', 'quiet', '-show_entries', 'format=duration',
            '-of', 'default=noprint_wrappers=1:nokey=1', input_path
        ]
        result = subprocess.run(command, capture_output=True, text=True, check=True)
        duration_str = result.stdout.strip()

        # Verificar si la salida es válida
        if not duration_str or duration_str.lower() in ['n/a', 'na', '']:
            print(f"No se pudo obtener duración para {input_path}")
            return None

        return float(duration_str)
    except (subprocess.CalledProcessError, ValueError, TypeError) as e:
        print(f"Error obteniendo duración: {e}")
        return None

def get_video_frame_count(input_path):
    """Obtiene el número total de frames del video."""
    try:
        command = [
            'ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
            '-count_frames', '-show_entries', 'stream=nb_frames',
            '-of', 'csv=p=0', input_path
        ]
        result = subprocess.run(command, capture_output=True, text=True, check=True)
        frame_count_str = result.stdout.strip()

        # Verificar si la salida es válida
        if not frame_count_str or frame_count_str.lower() in ['n/a', 'na', '']:
            print(f"No se pudo obtener número de frames para {input_path}")
            return None

        return int(frame_count_str)
    except (subprocess.CalledProcessError, ValueError, TypeError) as e:
        print(f"Error obteniendo frames: {e}")
        return None

def process_with_ffmpeg(input_path, output_path, filters, progress_callback=None):
    """
    Procesa video con FFmpeg y reporta progreso.
    progress_callback: función que recibe (current_time, total_duration, stage)
    """
    filter_string = ",".join(filters)
    command = [
        'ffmpeg', '-y',  # -y para sobrescribir sin preguntar
        '-i', input_path,
        '-vf', filter_string,
        '-c:v', 'libx264',
        '-crf', '18',
        '-preset', 'medium',  # Cambiar de 'slow' a 'medium' para mejor rendimiento
        '-progress', 'pipe:1',  # Enviar progreso a stdout
        output_path
    ]

    if progress_callback:
        duration = get_video_duration(input_path)
        if duration and duration > 0:
            # Procesamiento con monitoreo de progreso
            process = subprocess.Popen(command, stdout=subprocess.PIPE,
                                     stderr=subprocess.PIPE, text=True, bufsize=1)

            for line in process.stdout:
                line = line.strip()
                if line.startswith('out_time_ms='):
                    try:
                        time_ms_str = line.split('=')[1].strip()
                        if time_ms_str and time_ms_str.lower() not in ['n/a', 'na']:
                            time_ms = int(time_ms_str)
                            current_time = time_ms / 1000000.0  # Convertir microsegundos a segundos
                            progress_callback(current_time, duration, "FFmpeg")
                    except (ValueError, IndexError) as e:
                        print(f"Error parseando progreso FFmpeg: {e}")
                        continue
                elif line.startswith('progress=end'):
                    progress_callback(duration, duration, "FFmpeg")
                    break

            process.wait()
            if process.returncode != 0:
                stderr_output = process.stderr.read() if process.stderr else "Error desconocido"
                raise subprocess.CalledProcessError(process.returncode, command, stderr_output)
        else:
            # Sin monitoreo de progreso si no se puede obtener duración
            print("No se pudo obtener duración del video, procesando sin progreso...")
            if progress_callback:
                progress_callback(0, 100, "Procesando con FFmpeg...")
            subprocess.run(command, check=True)
            if progress_callback:
                progress_callback(100, 100, "FFmpeg completado")
    else:
        subprocess.run(command, check=True)

def process_with_realesrgan(input_path, output_path, model='realesrgan-x4plus', progress_callback=None):
    """
    Procesa video con Real-ESRGAN extrayendo frames, procesándolos y rearmando el video.
    Si Real-ESRGAN falla, usa FFmpeg como fallback.
    progress_callback: función que recibe (current_step, total_steps, stage)
    """
    import tempfile
    import shutil
    import glob

    # Crear directorio temporal para frames
    temp_dir = tempfile.mkdtemp()
    frames_dir = os.path.join(temp_dir, "frames")
    upscaled_dir = os.path.join(temp_dir, "upscaled")
    os.makedirs(frames_dir, exist_ok=True)
    os.makedirs(upscaled_dir, exist_ok=True)

    try:
        # Fase 1: Extraer frames del video
        if progress_callback:
            progress_callback(0, 100, "Extrayendo frames...")

        print("Extrayendo frames del video...")
        extract_command = [
            'ffmpeg', '-y', '-i', input_path,
            '-vf', 'fps=15',  # Reducir a 15 fps para mejor rendimiento
            os.path.join(frames_dir, 'frame_%06d.png')
        ]
        subprocess.run(extract_command, check=True)

        # Contar frames extraídos
        frame_files = glob.glob(os.path.join(frames_dir, '*.png'))
        total_frames = len(frame_files)

        if total_frames == 0:
            raise Exception("No se pudieron extraer frames del video. Verifica que el archivo sea válido.")

        if progress_callback:
            progress_callback(25, 100, f"Frames extraídos: {total_frames}")

        # Fase 2: Procesar frames con Real-ESRGAN
        print(f"Procesando frames con Real-ESRGAN (modelo: {model})...")
        models_path = os.path.join(REALESRGAN_PATH, "models")
        realesrgan_command = [
            os.path.join(REALESRGAN_PATH, "realesrgan-ncnn-vulkan"),
            "-i", frames_dir,
            "-o", upscaled_dir,
            "-m", models_path,
            "-n", model,
            "-f", "png",
            "-j", "1:1:1"  # Usar un solo hilo para evitar sobrecarga
        ]

        # Ejecutar Real-ESRGAN con monitoreo de progreso
        process = subprocess.Popen(realesrgan_command, stdout=subprocess.PIPE,
                                 stderr=subprocess.STDOUT, text=True, bufsize=1)

        for line in process.stdout:
            if progress_callback and total_frames > 0:
                # Buscar indicadores de progreso en la salida
                if "%" in line:
                    try:
                        # Extraer porcentaje de la línea
                        percent_match = re.search(r'(\d+(?:\.\d+)?)%', line)
                        if percent_match:
                            percent = float(percent_match.group(1))
                            overall_progress = 25 + (percent * 0.5)  # 25-75% del progreso total
                            progress_callback(int(overall_progress), 100, f"Procesando IA: {percent:.1f}%")
                    except ValueError:
                        pass

        process.wait()
        if process.returncode != 0:
            raise subprocess.CalledProcessError(process.returncode, realesrgan_command)

        if progress_callback:
            progress_callback(75, 100, "Reensamblando video...")

        # Fase 3: Rearmar video desde frames procesados
        print("Rearmando video desde frames procesados...")
        reassemble_command = [
            'ffmpeg', '-y', '-framerate', '15',
            '-i', os.path.join(upscaled_dir, 'frame_%06d.png'),
            '-c:v', 'libx264', '-crf', '18', '-preset', 'medium',
            '-pix_fmt', 'yuv420p',
            output_path
        ]
        subprocess.run(reassemble_command, check=True)

        if progress_callback:
            progress_callback(100, 100, "¡Completado!")

    except subprocess.CalledProcessError as e:
        print(f"Error con Real-ESRGAN: {e}")
        print("Usando FFmpeg como fallback...")
        if progress_callback:
            progress_callback(80, 100, "Error en IA, usando FFmpeg...")

        # Fallback a FFmpeg con upscaling de alta calidad
        fallback_command = [
            'ffmpeg', '-y', '-i', input_path,
            '-vf', 'scale=1920:1080:flags=lanczos',
            '-c:v', 'libx264', '-crf', '18', '-preset', 'medium',
            output_path
        ]
        subprocess.run(fallback_command, check=True)

        if progress_callback:
            progress_callback(100, 100, "Completado con FFmpeg")

    finally:
        # Limpiar directorio temporal
        shutil.rmtree(temp_dir, ignore_errors=True)
