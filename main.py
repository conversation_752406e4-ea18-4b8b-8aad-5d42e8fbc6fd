import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import subprocess
import threading
from processor import process_with_ffmpeg, process_with_realesrgan

# Variables globales
input_path = ""
output_path = ""

def browse_file():
    global input_path
    file = filedialog.askopenfilename(filetypes=[
        ("Videos", "*.mp4 *.avi *.mkv *.mov *.webm *.flv")])
    if file:
        input_path = file
        entry_input.delete(0, tk.END)
        entry_input.insert(0, file)

def view_file(path):
    if os.path.exists(path):
        subprocess.Popen(["xdg-open", path])  # Linux; usa 'start' (Windows) o 'open' (macOS)

def run_processing():
    global input_path, output_path

    if not input_path:
        messagebox.showerror("Error", "Selecciona un archivo de video.")
        return

    filters = []
    if grayscale.get():
        filters.append("hue=s=0")

    if sharpen.get():
        # Usar el valor del slider para nitidez
        level = sharpen_level.get()
        filters.append(f"unsharp=5:5:{level}:5:5:{level}")

    if denoise.get():
        # Usar el valor del slider para reducción de ruido
        level = denoise_level.get()
        filters.append(f"hqdn3d={level}")

    # Agregar resolución según la opción seleccionada
    resolution = resolution_var.get()
    if resolution != "original":
        filters.append(f"scale={resolution}:flags=lanczos")

    base = os.path.splitext(os.path.basename(input_path))[0]
    output_path = f"output/{base}_editado.mp4"
    os.makedirs("output", exist_ok=True)

    def process():
        try:
            progress_bar.start()

            if use_realesrgan.get():
                # Si se usa Real-ESRGAN, aplicar primero los filtros básicos y luego Real-ESRGAN
                model = model_var.get()

                # Si hay filtros básicos (sin resolución), aplicarlos primero
                basic_filters = [f for f in filters if not f.startswith("scale=")]

                if basic_filters:
                    # Crear archivo temporal con filtros básicos
                    temp_output = f"temp_{base}_filtered.mp4"
                    process_with_ffmpeg(input_path, temp_output, basic_filters)
                    # Luego aplicar Real-ESRGAN al archivo filtrado
                    process_with_realesrgan(temp_output, output_path, model=model)
                    # Limpiar archivo temporal
                    if os.path.exists(temp_output):
                        os.remove(temp_output)
                else:
                    # Solo Real-ESRGAN sin filtros previos
                    process_with_realesrgan(input_path, output_path, model=model)
            else:
                # Usar solo FFmpeg con todos los filtros
                process_with_ffmpeg(input_path, output_path, filters)

            progress_bar.stop()
            messagebox.showinfo("Éxito", f"Video exportado:\n{output_path}")
        except Exception as e:
            progress_bar.stop()
            messagebox.showerror("Error", str(e))

    threading.Thread(target=process).start()

# GUI
root = tk.Tk()
root.title("Mejorador de Video (FFmpeg + Real-ESRGAN)")
root.geometry("650x750")

# Entrada
tk.Label(root, text="Selecciona un video:").pack(pady=5)
entry_input = tk.Entry(root, width=50)
entry_input.pack()
tk.Button(root, text="Buscar", command=browse_file).pack(pady=5)

# Filtros
grayscale = tk.BooleanVar()
sharpen = tk.BooleanVar()
denoise = tk.BooleanVar()
upscale = tk.BooleanVar()
use_realesrgan = tk.BooleanVar()

# Variables para sliders
sharpen_level = tk.DoubleVar(value=1.0)
denoise_level = tk.DoubleVar(value=1.0)

# Filtros básicos
tk.Label(root, text="Filtros de Video:", font=("Arial", 12, "bold")).pack(pady=(10,5))
tk.Checkbutton(root, text="Escala de grises", variable=grayscale).pack(anchor='w', padx=20)

# Nitidez con slider
sharpen_frame = tk.Frame(root)
sharpen_frame.pack(anchor='w', padx=20, pady=2)
tk.Checkbutton(sharpen_frame, text="Nitidez:", variable=sharpen).pack(side='left')
tk.Scale(sharpen_frame, from_=0.1, to=3.0, resolution=0.1, orient='horizontal',
         variable=sharpen_level, length=200).pack(side='left', padx=(10,0))
tk.Label(sharpen_frame, text="(0.1=suave, 3.0=muy nítido)").pack(side='left', padx=(5,0))

# Reducción de ruido con slider
denoise_frame = tk.Frame(root)
denoise_frame.pack(anchor='w', padx=20, pady=2)
tk.Checkbutton(denoise_frame, text="Reducción de ruido:", variable=denoise).pack(side='left')
tk.Scale(denoise_frame, from_=0.1, to=10.0, resolution=0.1, orient='horizontal',
         variable=denoise_level, length=200).pack(side='left', padx=(10,0))
tk.Label(denoise_frame, text="(0.1=suave, 10.0=agresivo)").pack(side='left', padx=(5,0))

# Real-ESRGAN
tk.Checkbutton(root, text="Usar Real-ESRGAN (IA) para upscaling", variable=use_realesrgan).pack(anchor='w', padx=20, pady=5)

# Resolución
tk.Label(root, text="Resolución de salida:", font=("Arial", 12, "bold")).pack(pady=(15,5))
resolution_var = tk.StringVar(value="original")
resolution_options = [
    ("Original (sin cambios)", "original"),
    ("1K (1280x720)", "1280:720"),
    ("2K (1920x1080)", "1920:1080"),
    ("4K (3840x2160)", "3840:2160")
]

for text, value in resolution_options:
    tk.Radiobutton(root, text=text, variable=resolution_var, value=value).pack(anchor='w', padx=20)

# Modelos Real-ESRGAN
tk.Label(root, text="Modelo Real-ESRGAN:", font=("Arial", 10, "bold")).pack(pady=(10,5))
model_var = tk.StringVar(value="realesrgan-x4plus")
model_options = [
    "realesrgan-x4plus",
    "realesrgan-x2plus",
    "realesrgan-x4plus-anime",
    "realesr-animevideov3",
    "realesr-general-wdn-x4v3",
    "realesr-general-x4v3"
]
tk.OptionMenu(root, model_var, *model_options).pack()

# Proceso
tk.Button(root, text="Procesar Video", command=run_processing).pack(pady=10)

progress_bar = ttk.Progressbar(root, orient='horizontal', length=400, mode='indeterminate')
progress_bar.pack(pady=10)

# Vista previa
tk.Button(root, text="Ver Original", command=lambda: view_file(input_path)).pack(pady=2)
tk.Button(root, text="Ver Editado", command=lambda: view_file(output_path)).pack(pady=2)

root.mainloop()
