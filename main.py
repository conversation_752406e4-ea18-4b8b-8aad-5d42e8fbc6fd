import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
import subprocess
import threading
import queue
import time
from processor import process_with_ffmpeg, process_with_realesrgan

# Variables globales
input_path = ""
output_path = ""
processing_thread = None
progress_queue = queue.Queue()
is_processing = False

def browse_file():
    global input_path
    file = filedialog.askopenfilename(filetypes=[
        ("Videos", "*.mp4 *.avi *.mkv *.mov *.webm *.flv")])
    if file:
        input_path = file
        entry_input.delete(0, tk.END)
        entry_input.insert(0, file)
        print(f"📁 Archivo seleccionado: {file}")

        # Mostrar información básica del archivo
        try:
            file_size = os.path.getsize(file) / (1024*1024)  # MB
            print(f"   Tamaño: {file_size:.2f} MB")
        except Exception as e:
            print(f"   Error obteniendo tamaño: {e}")

def view_file(path):
    if os.path.exists(path):
        subprocess.Popen(["xdg-open", path])  # Linux; usa 'start' (Windows) o 'open' (macOS)

def update_progress():
    """Actualiza la barra de progreso basada en la cola de mensajes."""
    try:
        while True:
            progress_data = progress_queue.get_nowait()
            current, total, message = progress_data

            # Actualizar barra de progreso
            progress_percentage = (current / total) * 100 if total > 0 else 0
            progress_bar['value'] = progress_percentage

            # Actualizar etiqueta de estado
            status_label.config(text=f"{message} ({progress_percentage:.1f}%)")

            # Actualizar la ventana
            root.update_idletasks()

    except queue.Empty:
        pass

    # Programar la próxima actualización si aún se está procesando
    if is_processing:
        root.after(100, update_progress)  # Actualizar cada 100ms

def progress_callback(current, total, message):
    """Callback para recibir actualizaciones de progreso desde el procesador."""
    progress_queue.put((current, total, message))

def validate_input():
    """Valida el archivo de entrada antes del procesamiento."""
    if not input_path:
        messagebox.showerror("Error", "Selecciona un archivo de video.")
        return False

    if not os.path.exists(input_path):
        messagebox.showerror("Error", "El archivo seleccionado no existe.")
        return False

    # Verificar que el archivo tenga una extensión de video válida
    valid_extensions = ['.mp4', '.avi', '.mkv', '.mov', '.webm', '.flv']
    file_ext = os.path.splitext(input_path)[1].lower()
    if file_ext not in valid_extensions:
        messagebox.showerror("Error", f"Formato de archivo no soportado: {file_ext}")
        return False

    return True

def check_dependencies():
    """Verifica que las dependencias necesarias estén instaladas."""
    try:
        # Verificar FFmpeg
        print("🔍 Verificando FFmpeg...")
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        print("✅ FFmpeg encontrado")

        print("🔍 Verificando FFprobe...")
        subprocess.run(['ffprobe', '-version'], capture_output=True, check=True)
        print("✅ FFprobe encontrado")

        return True
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        print(f"❌ Error verificando dependencias: {e}")
        print(f"   Tipo de error: {type(e).__name__}")

        messagebox.showerror("Dependencias faltantes",
                           "FFmpeg no está instalado o no se encuentra en el PATH.\n\n"
                           "Para instalar FFmpeg:\n"
                           "Ubuntu/Debian: sudo apt install ffmpeg\n"
                           "Windows: Descargar desde https://ffmpeg.org/\n"
                           "macOS: brew install ffmpeg")
        return False

def cancel_processing():
    """Cancela el procesamiento actual."""
    global is_processing, processing_thread

    if is_processing and processing_thread and processing_thread.is_alive():
        # Nota: En Python es difícil terminar hilos de forma segura
        # Esta función principalmente actualiza la UI
        is_processing = False
        process_button.config(state='normal', text="Procesar Video")
        cancel_button.config(state='disabled')
        status_label.config(text="Procesamiento cancelado por el usuario")
        progress_bar.config(value=0, mode='indeterminate')
        messagebox.showinfo("Cancelado", "Procesamiento cancelado. El archivo puede estar incompleto.")

def on_closing():
    """Maneja el cierre de la aplicación."""
    global is_processing

    if is_processing:
        if messagebox.askokcancel("Salir", "Hay un procesamiento en curso. ¿Deseas salir de todas formas?"):
            is_processing = False
            root.destroy()
    else:
        root.destroy()

def run_processing():
    global input_path, output_path, processing_thread, is_processing

    # Verificar dependencias
    if not check_dependencies():
        return

    # Validar entrada
    if not validate_input():
        return

    # Verificar si ya hay un procesamiento en curso
    if is_processing:
        messagebox.showwarning("Advertencia", "Ya hay un procesamiento en curso.")
        return

    # Preparar filtros
    filters = []
    if grayscale.get():
        filters.append("hue=s=0")

    if sharpen.get():
        level = sharpen_level.get()
        filters.append(f"unsharp=5:5:{level}:5:5:{level}")

    if denoise.get():
        level = denoise_level.get()
        filters.append(f"hqdn3d={level}")

    # Agregar resolución según la opción seleccionada
    resolution = resolution_var.get()
    if resolution != "original":
        filters.append(f"scale={resolution}:flags=lanczos")

    base = os.path.splitext(os.path.basename(input_path))[0]
    output_path = f"output/{base}_editado.mp4"
    os.makedirs("output", exist_ok=True)

    def process():
        global is_processing
        try:
            is_processing = True

            print(f"\n🚀 INICIANDO PROCESAMIENTO")
            print(f"   Archivo: {input_path}")
            print(f"   Filtros: {filters}")
            print(f"   Real-ESRGAN: {use_realesrgan.get()}")
            if use_realesrgan.get():
                print(f"   Modelo: {model_var.get()}")
            print(f"   Resolución: {resolution}")

            # Configurar barra de progreso
            progress_bar.config(mode='determinate', maximum=100, value=0)
            status_label.config(text="Iniciando procesamiento...")

            # Deshabilitar botón de procesamiento y habilitar cancelación
            process_button.config(state='disabled', text="Procesando...")
            cancel_button.config(state='normal')

            # Iniciar actualización de progreso
            root.after(100, update_progress)

            if use_realesrgan.get():
                # Si se usa Real-ESRGAN, aplicar primero los filtros básicos y luego Real-ESRGAN
                model = model_var.get()
                basic_filters = [f for f in filters if not f.startswith("scale=")]

                if basic_filters:
                    # Crear archivo temporal con filtros básicos
                    temp_output = f"temp_{base}_filtered.mp4"
                    process_with_ffmpeg(input_path, temp_output, basic_filters, progress_callback)
                    # Luego aplicar Real-ESRGAN al archivo filtrado
                    process_with_realesrgan(temp_output, output_path, model=model, progress_callback=progress_callback)
                    # Limpiar archivo temporal
                    if os.path.exists(temp_output):
                        os.remove(temp_output)
                else:
                    # Solo Real-ESRGAN sin filtros previos
                    process_with_realesrgan(input_path, output_path, model=model, progress_callback=progress_callback)
            else:
                # Usar solo FFmpeg con todos los filtros
                process_with_ffmpeg(input_path, output_path, filters, progress_callback)

            # Finalizar procesamiento
            progress_bar.config(value=100)
            status_label.config(text="¡Procesamiento completado!")
            messagebox.showinfo("Éxito", f"Video exportado:\n{output_path}")

        except Exception as e:
            progress_bar.config(value=0)
            status_label.config(text="Error en el procesamiento", fg="red")

            # Imprimir error completo en consola para debugging
            print(f"\n❌ ERROR EN PROCESAMIENTO:")
            print(f"   Tipo: {type(e).__name__}")
            print(f"   Mensaje: {str(e)}")
            print(f"   Archivo: {input_path}")
            print(f"   Filtros aplicados: {filters}")
            print(f"   Usando Real-ESRGAN: {use_realesrgan.get()}")
            if use_realesrgan.get():
                print(f"   Modelo: {model_var.get()}")

            # Crear mensaje de error más informativo para el usuario
            error_msg = str(e)
            if "invalid literal for int()" in error_msg:
                error_msg = "Error: No se pudo obtener información del video.\nVerifica que el archivo sea un video válido."
            elif "No such file or directory" in error_msg:
                error_msg = "Error: FFmpeg o Real-ESRGAN no están instalados correctamente."
            elif "No se pudieron extraer frames" in error_msg:
                error_msg = "Error: El archivo de video parece estar corrupto o en un formato no soportado."
            elif "Permission denied" in error_msg:
                error_msg = "Error: Sin permisos para escribir en el directorio de salida."

            messagebox.showerror("Error durante el procesamiento", error_msg)
        finally:
            # Restaurar estado de la interfaz
            is_processing = False
            process_button.config(state='normal', text="Procesar Video")
            cancel_button.config(state='disabled')
            progress_bar.config(mode='indeterminate')

    # Iniciar procesamiento en hilo separado
    processing_thread = threading.Thread(target=process, daemon=True)
    processing_thread.start()

# Inicialización y logging
print("🎬 Iniciando VideoApp - Mejorador de Video con IA")
print("=" * 50)

# GUI
root = tk.Tk()
root.title("Mejorador de Video (FFmpeg + Real-ESRGAN)")
root.geometry("650x750")

# Entrada
tk.Label(root, text="Selecciona un video:").pack(pady=5)
entry_input = tk.Entry(root, width=50)
entry_input.pack()
tk.Button(root, text="Buscar", command=browse_file).pack(pady=5)

# Filtros
grayscale = tk.BooleanVar()
sharpen = tk.BooleanVar()
denoise = tk.BooleanVar()
upscale = tk.BooleanVar()
use_realesrgan = tk.BooleanVar()

# Variables para sliders
sharpen_level = tk.DoubleVar(value=1.0)
denoise_level = tk.DoubleVar(value=1.0)

# Filtros básicos
tk.Label(root, text="Filtros de Video:", font=("Arial", 12, "bold")).pack(pady=(10,5))
tk.Checkbutton(root, text="Escala de grises", variable=grayscale).pack(anchor='w', padx=20)

# Nitidez con slider
sharpen_frame = tk.Frame(root)
sharpen_frame.pack(anchor='w', padx=20, pady=2)
tk.Checkbutton(sharpen_frame, text="Nitidez:", variable=sharpen).pack(side='left')
tk.Scale(sharpen_frame, from_=0.1, to=3.0, resolution=0.1, orient='horizontal',
         variable=sharpen_level, length=200).pack(side='left', padx=(10,0))
tk.Label(sharpen_frame, text="(0.1=suave, 3.0=muy nítido)").pack(side='left', padx=(5,0))

# Reducción de ruido con slider
denoise_frame = tk.Frame(root)
denoise_frame.pack(anchor='w', padx=20, pady=2)
tk.Checkbutton(denoise_frame, text="Reducción de ruido:", variable=denoise).pack(side='left')
tk.Scale(denoise_frame, from_=0.1, to=10.0, resolution=0.1, orient='horizontal',
         variable=denoise_level, length=200).pack(side='left', padx=(10,0))
tk.Label(denoise_frame, text="(0.1=suave, 10.0=agresivo)").pack(side='left', padx=(5,0))

# Real-ESRGAN
tk.Checkbutton(root, text="Usar Real-ESRGAN (IA) para upscaling", variable=use_realesrgan).pack(anchor='w', padx=20, pady=5)

# Resolución
tk.Label(root, text="Resolución de salida:", font=("Arial", 12, "bold")).pack(pady=(15,5))
resolution_var = tk.StringVar(value="original")
resolution_options = [
    ("Original (sin cambios)", "original"),
    ("1K (1280x720)", "1280:720"),
    ("2K (1920x1080)", "1920:1080"),
    ("4K (3840x2160)", "3840:2160")
]

for text, value in resolution_options:
    tk.Radiobutton(root, text=text, variable=resolution_var, value=value).pack(anchor='w', padx=20)

# Modelos Real-ESRGAN
tk.Label(root, text="Modelo Real-ESRGAN:", font=("Arial", 10, "bold")).pack(pady=(10,5))
model_var = tk.StringVar(value="realesrgan-x4plus")
model_options = [
    "realesrgan-x4plus",
    "realesrgan-x2plus",
    "realesrgan-x4plus-anime",
    "realesr-animevideov3",
    "realesr-general-wdn-x4v3",
    "realesr-general-x4v3"
]
tk.OptionMenu(root, model_var, *model_options).pack()

# Proceso
button_frame = tk.Frame(root)
button_frame.pack(pady=10)

process_button = tk.Button(button_frame, text="Procesar Video", command=run_processing,
                          bg="#4CAF50", fg="white", font=("Arial", 10, "bold"))
process_button.pack(side='left', padx=(0,10))

cancel_button = tk.Button(button_frame, text="Cancelar", command=cancel_processing,
                         bg="#f44336", fg="white", state='disabled')
cancel_button.pack(side='left')

# Barra de progreso y estado
progress_frame = tk.Frame(root)
progress_frame.pack(pady=10)

progress_bar = ttk.Progressbar(progress_frame, orient='horizontal', length=400, mode='indeterminate')
progress_bar.pack()

status_label = tk.Label(progress_frame, text="Listo para procesar", fg="blue")
status_label.pack(pady=(5,0))

# Vista previa
tk.Button(root, text="Ver Original", command=lambda: view_file(input_path)).pack(pady=2)
tk.Button(root, text="Ver Editado", command=lambda: view_file(output_path)).pack(pady=2)

# Configurar el protocolo de cierre
root.protocol("WM_DELETE_WINDOW", on_closing)

# Mejorar el título de la ventana
root.title("🎬 VideoApp - Mejorador de Video con IA (Optimizado)")

root.mainloop()
