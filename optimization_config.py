# Configuración de optimizaciones para VideoApp

# Configuraciones de calidad para FFmpeg
QUALITY_PRESETS = {
    "alta": {
        "crf": 18,
        "preset": "slow",
        "description": "Máxima calidad, procesamiento lento"
    },
    "media": {
        "crf": 23,
        "preset": "medium", 
        "description": "Calidad balanceada, velocidad media"
    },
    "rapida": {
        "crf": 28,
        "preset": "fast",
        "description": "Calidad básica, procesamiento rápido"
    }
}

# Configuraciones de Real-ESRGAN
REALESRGAN_SETTINGS = {
    "fps_limit": 15,  # Reducido de 30 a 15 para mejor rendimiento
    "tile_size": 256,  # Tamaño de tile para GPU con poca memoria
    "threads": "1:1:1",  # Configuración conservadora de hilos
}

# Configuraciones de la interfaz
UI_SETTINGS = {
    "progress_update_interval": 100,  # ms
    "window_size": "650x800",  # Aumentado para acomodar nuevos elementos
    "colors": {
        "process_button": "#4CAF50",
        "cancel_button": "#f44336", 
        "status_ready": "blue",
        "status_processing": "orange",
        "status_complete": "green",
        "status_error": "red"
    }
}

# Límites de seguridad
SAFETY_LIMITS = {
    "max_file_size_mb": 500,  # Máximo tamaño de archivo en MB
    "max_duration_minutes": 10,  # Máxima duración en minutos para Real-ESRGAN
    "min_free_space_gb": 2,  # Espacio libre mínimo requerido en GB
}
