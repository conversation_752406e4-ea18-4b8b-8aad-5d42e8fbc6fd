# 🎬 VideoApp - Mejorador de Video con IA

Una aplicación de escritorio para mejorar videos usando FFmpeg y Real-ESRGAN con inteligencia artificial.

## 📋 Descripción

VideoApp es una herramienta de procesamiento de video que combina filtros tradicionales de FFmpeg con tecnología de upscaling basada en IA (Real-ESRGAN). Permite aplicar múltiples mejoras a videos de forma sencilla a través de una interfaz gráfica intuitiva.

## ✨ Características

### 🎛️ Filtros de Video
- **Escala de grises**: Convierte el video a blanco y negro
- **Nitidez ajustable**: Control deslizante de 0.1 a 3.0 para ajustar la nitidez
- **Reducción de ruido**: Control deslizante de 0.1 a 10.0 para eliminar ruido
- **Escalado de resolución**: Opciones predefinidas (1K, 2K, 4K) o mantener original

### 🤖 IA Real-ESRGAN
- **Upscaling inteligente**: Mejora la calidad usando redes neuronales
- **<PERSON>úl<PERSON>les modelos disponibles**:
  - `realesrgan-x4plus`: Propósito general (4x)
  - `realesrgan-x4plus-anime`: Optimizado para anime (4x)
  - `realesr-animevideov3`: Optimizado para video (2x, 3x, 4x)
- **Fallback automático**: Si Real-ESRGAN falla, usa FFmpeg como respaldo

### 🔧 Funcionalidades Técnicas
- **Procesamiento en paralelo**: Usa threading para no bloquear la interfaz
- **Gestión de archivos temporales**: Limpieza automática de archivos intermedios
- **Barra de progreso**: Indicador visual del procesamiento
- **Vista previa**: Botones para abrir videos original y procesado
- **Múltiples formatos**: Soporte para MP4, AVI, MKV, MOV, WebM, FLV

## 🏗️ Arquitectura

### Estructura de Archivos
```
VideoApp/
├── main.py                    # Interfaz gráfica principal
├── processor.py               # Lógica de procesamiento de video
├── config.py                  # Configuración de rutas
├── realesrgan-ncnn-vulkan/    # Ejecutable y modelos de IA
│   ├── realesrgan-ncnn-vulkan # Ejecutable principal
│   └── models/                # Modelos de redes neuronales
│       ├── realesrgan-x4plus.bin/param
│       ├── realesrgan-x4plus-anime.bin/param
│       └── realesr-animevideov3-x*.bin/param
├── output/                    # Videos procesados
└── __pycache__/              # Cache de Python
```

### Componentes Principales

#### 1. **main.py** - Interfaz Gráfica
- **Framework**: Tkinter nativo de Python
- **Diseño**: Layout vertical con secciones organizadas
- **Controles interactivos**: Checkboxes, sliders, radio buttons
- **Gestión de estado**: Variables globales para rutas y configuración

#### 2. **processor.py** - Motor de Procesamiento
- **FFmpeg Integration**: Función `process_with_ffmpeg()`
- **Real-ESRGAN Pipeline**: Función `process_with_realesrgan()`
- **Workflow de IA**:
  1. Extracción de frames (30 FPS)
  2. Procesamiento con Real-ESRGAN
  3. Reensamblado del video
- **Error Handling**: Fallback automático a FFmpeg

#### 3. **config.py** - Configuración
- **Gestión de rutas**: Path dinámico a Real-ESRGAN
- **Portabilidad**: Rutas relativas al directorio de la aplicación

## 🚀 Instalación

### Prerrequisitos
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install python3 python3-tk ffmpeg

# Verificar instalación
python3 --version
ffmpeg -version
```

### Configuración
1. **Clonar/Descargar** el proyecto
2. **Verificar estructura** de archivos
3. **Ejecutar** la aplicación:
```bash
cd VideoApp
python3 main.py
```

## 📖 Uso

### Flujo Básico
1. **Seleccionar video**: Usar botón "Buscar" para elegir archivo
2. **Configurar filtros**: Activar/ajustar filtros deseados
3. **Elegir resolución**: Seleccionar resolución de salida
4. **Opcional - IA**: Activar Real-ESRGAN y elegir modelo
5. **Procesar**: Hacer clic en "Procesar Video"
6. **Resultado**: El video se guarda en la carpeta `output/`

### Recomendaciones de Uso
- **Videos cortos**: Real-ESRGAN es intensivo, usar en clips cortos
- **Modelos por tipo**:
  - Anime/Dibujos: `realesrgan-x4plus-anime`
  - Video real: `realesr-animevideov3`
  - Propósito general: `realesrgan-x4plus`
- **Combinación de filtros**: Se pueden usar múltiples filtros simultáneamente

## 🔧 Configuración Avanzada

### Parámetros FFmpeg
- **Codec**: H.264 (libx264)
- **CRF**: 18 (alta calidad)
- **Preset**: slow (mejor compresión)
- **Pixel Format**: yuv420p (compatibilidad)

### Real-ESRGAN Settings
- **Tile Size**: Auto (optimización automática)
- **GPU**: Auto-detección
- **Formato**: PNG para frames intermedios
- **FPS**: Limitado a 30 para eficiencia

## ⚡ Rendimiento

### Benchmarks Aproximados
- **FFmpeg solo**: ~1-5x velocidad real
- **Real-ESRGAN**: ~0.1-0.5x velocidad real (depende de GPU)
- **Memoria**: ~500MB-2GB (según resolución)
- **Almacenamiento temporal**: ~100MB-1GB por minuto de video

### Optimizaciones Implementadas
- **FPS limitado**: 30 FPS máximo para Real-ESRGAN
- **Limpieza automática**: Eliminación de archivos temporales
- **Threading**: Procesamiento no bloqueante
- **Fallback**: Recuperación automática de errores

## 🐛 Problemas Conocidos

### Limitaciones Actuales
1. **Interfaz básica**: Tkinter es funcional pero no moderna
2. **Sin preview en tiempo real**: No hay vista previa durante procesamiento
3. **Progreso impreciso**: Barra de progreso indeterminada
4. **Un video a la vez**: No procesamiento por lotes
5. **Configuración limitada**: Parámetros FFmpeg fijos
6. **Sin validación robusta**: Verificación mínima de archivos de entrada

### Errores Comunes
- **"find_blob_index_by_name failed"**: Modelos Real-ESRGAN faltantes (ya solucionado)
- **FFmpeg no encontrado**: Instalar FFmpeg en el sistema
- **Memoria insuficiente**: Videos muy largos o alta resolución
- **GPU no compatible**: Real-ESRGAN requiere GPU compatible con Vulkan

## 🚀 Mejoras Propuestas

### 🎨 Interfaz de Usuario
1. **Modernizar UI**:
   - Migrar a PyQt6/PySide6 o tkinter.ttk mejorado
   - Tema oscuro/claro
   - Iconos y mejor tipografía
   - Layout responsive

2. **Preview en tiempo real**:
   - Ventana de vista previa con antes/después
   - Scrubber para navegar por el video
   - Zoom y pan en la preview

3. **Mejor feedback**:
   - Progreso real con porcentaje y tiempo estimado
   - Log de procesamiento visible
   - Notificaciones del sistema

### ⚙️ Funcionalidad
4. **Procesamiento por lotes**:
   - Cola de videos para procesar
   - Configuraciones por video
   - Procesamiento automático nocturno

5. **Más filtros**:
   - Corrección de color (brillo, contraste, saturación)
   - Estabilización de video
   - Recorte y rotación
   - Filtros artísticos

6. **Configuración avanzada**:
   - Parámetros FFmpeg personalizables
   - Perfiles de configuración guardables
   - Configuración de GPU para Real-ESRGAN

### 🔧 Arquitectura
7. **Mejor gestión de errores**:
   - Logging estructurado
   - Recuperación de errores más robusta
   - Validación de entrada completa

8. **Optimización de rendimiento**:
   - Procesamiento paralelo de frames
   - Cache inteligente
   - Compresión adaptativa

9. **Extensibilidad**:
   - Sistema de plugins
   - API para filtros personalizados
   - Soporte para más modelos de IA

### 📱 Distribución
10. **Empaquetado**:
    - Ejecutable standalone (PyInstaller)
    - Instalador para Windows/macOS/Linux
    - Distribución via pip/conda

11. **Documentación**:
    - Manual de usuario completo
    - Tutoriales en video
    - API documentation

## 🛠️ Implementación de Mejoras

### Prioridad Alta (Impacto inmediato)
```python
# 1. Progreso real en lugar de indeterminado
def update_progress(current_frame, total_frames):
    progress = (current_frame / total_frames) * 100
    progress_bar.configure(value=progress)

# 2. Validación de entrada
def validate_input_file(file_path):
    if not os.path.exists(file_path):
        raise FileNotFoundError("Archivo no encontrado")
    # Verificar formato con ffprobe

# 3. Configuración de calidad
quality_options = {
    "alta": {"crf": 18, "preset": "slow"},
    "media": {"crf": 23, "preset": "medium"},
    "rapida": {"crf": 28, "preset": "fast"}
}
```

### Prioridad Media (Mejoras UX)
```python
# 4. Preview con thumbnails
def generate_thumbnail(video_path, timestamp=5):
    cmd = ["ffmpeg", "-i", video_path, "-ss", str(timestamp),
           "-vframes", "1", "-f", "image2", "thumb.jpg"]

# 5. Procesamiento por lotes
class BatchProcessor:
    def __init__(self):
        self.queue = []
        self.current_job = None

    def add_job(self, input_path, settings):
        self.queue.append({"input": input_path, "settings": settings})
```

### Prioridad Baja (Funcionalidad avanzada)
```python
# 6. Sistema de plugins
class FilterPlugin:
    def __init__(self, name, description):
        self.name = name
        self.description = description

    def apply(self, input_path, output_path, **kwargs):
        raise NotImplementedError

# 7. Configuración persistente
import json

class Settings:
    def __init__(self):
        self.config_file = "settings.json"
        self.load_settings()

    def save_settings(self):
        with open(self.config_file, 'w') as f:
            json.dump(self.settings, f)
```

## 📊 Métricas de Calidad

### Cobertura de Funcionalidades
- ✅ Procesamiento básico de video (100%)
- ✅ Filtros esenciales (80%)
- ✅ Real-ESRGAN integration (90%)
- ⚠️ Gestión de errores (60%)
- ❌ Interfaz moderna (20%)
- ❌ Procesamiento por lotes (0%)

### Usabilidad
- **Facilidad de uso**: 8/10
- **Estabilidad**: 7/10
- **Rendimiento**: 6/10
- **Documentación**: 5/10

## 🤝 Contribución

### Para Desarrolladores
1. **Fork** el repositorio
2. **Crear rama** para nueva funcionalidad
3. **Implementar** con tests
4. **Documentar** cambios
5. **Pull request** con descripción detallada

### Áreas que necesitan ayuda
- UI/UX design
- Optimización de rendimiento
- Testing automatizado
- Documentación
- Soporte multiplataforma

## 📄 Licencia

Este proyecto está bajo licencia MIT. Ver archivo LICENSE para detalles.

## 🙏 Agradecimientos

- **FFmpeg**: Por la potente biblioteca de procesamiento multimedia
- **Real-ESRGAN**: Por los modelos de IA de upscaling
- **Python/Tkinter**: Por la facilidad de desarrollo de GUI
- **Comunidad open source**: Por las herramientas y recursos disponibles
