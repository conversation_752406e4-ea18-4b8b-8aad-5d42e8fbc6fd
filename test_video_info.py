#!/usr/bin/env python3
"""
Script de prueba para verificar las funciones de obtención de información de video.
Útil para diagnosticar problemas con archivos específicos.
"""

import sys
import os
from processor import get_video_duration, get_video_frame_count

def test_video_info(video_path):
    """Prueba las funciones de obtención de información de video."""
    print(f"🎬 Analizando video: {video_path}")
    print("=" * 50)
    
    # Verificar que el archivo existe
    if not os.path.exists(video_path):
        print(f"❌ Error: El archivo {video_path} no existe")
        return False
    
    # Obtener duración
    print("📏 Obteniendo duración...")
    duration = get_video_duration(video_path)
    if duration:
        print(f"✅ Duración: {duration:.2f} segundos ({duration/60:.2f} minutos)")
    else:
        print("❌ No se pudo obtener la duración")
    
    # Obtener número de frames
    print("\n🎞️ Obteniendo número de frames...")
    frame_count = get_video_frame_count(video_path)
    if frame_count:
        print(f"✅ Frames totales: {frame_count}")
        if duration:
            fps = frame_count / duration
            print(f"📊 FPS estimado: {fps:.2f}")
    else:
        print("❌ No se pudo obtener el número de frames")
    
    # Información del archivo
    print(f"\n📁 Tamaño del archivo: {os.path.getsize(video_path) / (1024*1024):.2f} MB")
    
    return duration is not None or frame_count is not None

def main():
    """Función principal."""
    if len(sys.argv) != 2:
        print("Uso: python3 test_video_info.py <ruta_del_video>")
        print("\nEjemplo:")
        print("python3 test_video_info.py simplescreenrecorder-2025-06-30_16.27.45.mp4")
        return
    
    video_path = sys.argv[1]
    success = test_video_info(video_path)
    
    if success:
        print("\n✅ Prueba completada - El video es compatible")
    else:
        print("\n❌ Prueba fallida - Puede haber problemas con este video")
        print("\nPosibles soluciones:")
        print("1. Verificar que FFmpeg esté instalado: ffmpeg -version")
        print("2. Verificar que el archivo sea un video válido")
        print("3. Intentar con un formato diferente (MP4, AVI, etc.)")

if __name__ == "__main__":
    main()
