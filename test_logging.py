#!/usr/bin/env python3
"""
Script de prueba para verificar que el logging funciona correctamente.
"""

import sys
import os
from processor import get_video_duration, get_video_frame_count, process_with_ffmpeg

def test_logging():
    """Prueba las funciones con logging mejorado."""
    print("🧪 INICIANDO PRUEBAS DE LOGGING")
    print("=" * 50)
    
    # Verificar si hay un video de prueba
    test_files = [
        "simplescreenrecorder-2025-06-30_16.27.45.mp4",
        "output/simplescreenrecorder-2025-06-30_16.27.45_editado.mp4"
    ]
    
    video_file = None
    for file in test_files:
        if os.path.exists(file):
            video_file = file
            break
    
    if not video_file:
        print("❌ No se encontró ningún archivo de video para probar")
        print("   Archivos buscados:")
        for file in test_files:
            print(f"   - {file}")
        return False
    
    print(f"📁 Usando archivo de prueba: {video_file}")
    
    # Prueba 1: Obtener duración
    print("\n🔍 Prueba 1: Obtener duración del video")
    duration = get_video_duration(video_file)
    if duration:
        print(f"✅ Duración obtenida: {duration:.2f} segundos")
    else:
        print("❌ No se pudo obtener la duración")
    
    # Prueba 2: Obtener número de frames
    print("\n🔍 Prueba 2: Obtener número de frames")
    frame_count = get_video_frame_count(video_file)
    if frame_count:
        print(f"✅ Frames obtenidos: {frame_count}")
    else:
        print("❌ No se pudo obtener el número de frames")
    
    # Prueba 3: Simular error con archivo inexistente
    print("\n🔍 Prueba 3: Probar con archivo inexistente")
    fake_duration = get_video_duration("archivo_inexistente.mp4")
    if fake_duration is None:
        print("✅ Error manejado correctamente para archivo inexistente")
    
    # Prueba 4: Callback de progreso
    print("\n🔍 Prueba 4: Callback de progreso")
    def test_callback(current, total, message):
        percentage = (current / total) * 100 if total > 0 else 0
        print(f"   📊 Progreso: {percentage:.1f}% - {message}")
    
    print("   Simulando callback...")
    test_callback(25, 100, "Prueba de callback")
    test_callback(50, 100, "Mitad del proceso")
    test_callback(100, 100, "Completado")
    
    print("\n✅ PRUEBAS DE LOGGING COMPLETADAS")
    return True

if __name__ == "__main__":
    success = test_logging()
    if success:
        print("\n🎉 Todas las pruebas pasaron correctamente")
        print("   El logging está funcionando como esperado")
    else:
        print("\n❌ Algunas pruebas fallaron")
        print("   Revisa los mensajes de error arriba")
